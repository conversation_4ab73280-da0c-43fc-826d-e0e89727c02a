<template>
  <div class="comparison-container">
    <el-row :gutter="10">
      <el-col>
        <SubTitle title="各组行驶里程对比"></SubTitle>
      </el-col>
      <el-col>
        <TimeScreen class="mt-16"></TimeScreen>
      </el-col>
    </el-row>
    <div class="chart-box">
      <div class="chart" id="comparison-chart"></div>
    </div>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
  };
</script>

<style lang="scss" scoped>
  .comparison-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .chart-box {
    width: 100%;
    height: 400px;
    .chart {
      width: 100%;
      height: 100%;
    }
  }
</style>
